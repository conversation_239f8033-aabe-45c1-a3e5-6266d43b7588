import React from 'react';

interface Props {
  className?: string;
  size: number;
  animate?: boolean;
}

export const StarIcon: React.FC<Props> = ({ size, animate, className }: Props) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 16 16"
      version="1.1"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="m14.5996 7.1901c14.5995 7.11268 14.578 7.0368 14.5373 6.97092C14.4967 6.90503 14.4386 6.85171 14.3694 6.81691L11.4383 5.34836L9.97291 2.41153C9.83126 2.12845 9.36908 2.12845 9.2277 2.41153L7.76227 5.34836L4.83117 6.81691C4.76164 6.85139 4.70311 6.90461 4.6622 6.97057C4.62129 7.03653 4.59961 7.11261 4.59961 7.19023C4.59961 7.26785 4.62129 7.34393 4.6622 7.40989C4.70311 7.47585 4.76164 7.52907 4.83117 7.56355L7.76227 9.0321L9.2277 11.9689C9.2623 12.0381 9.31549 12.0963 9.3813 12.137C9.44711 12.1777 9.52294 12.1992 9.6003 12.1992C9.67766 12.1992 9.75349 12.1777 9.8193 12.137C9.88511 12.0963 9.9383 12.0381 9.97291 11.9689L11.4383 9.0321L14.3694 7.56407C14.4387 7.52918 14.4969 7.47574 14.5375 7.40971C14.5781 7.34367 14.5996 7.26764 14.5996 7.1901Z">
        {animate && (
          <animate
            attributeType="XML"
            attributeName="fill-opacity"
            values="1;0;1"
            keyTimes="0;0.5;1"
            dur="1.8s"
            repeatCount="indefinite"
          ></animate>
        )}
      </path>
      <path
        d="M3.09288 3.86686L1.85165 3.3492C1.82397 3.33536 1.80069 3.31408 1.78442 3.28776C1.76815 3.26143 1.75954 3.2311 1.75954 3.20015C1.75954 3.1692 1.76815 3.13887 1.78442 3.11255C1.80069 3.08622 1.82397 3.06494 1.85165 3.0511L3.09288 2.53365L3.61037 1.29251C3.6242 1.26478 3.64548 1.24145 3.67183 1.22514C3.69817 1.20883 3.72855 1.2002 3.75954 1.2002C3.79053 1.2002 3.8209 1.20883 3.84725 1.22514C3.8736 1.24145 3.89488 1.26478 3.9087 1.29251L4.42619 2.53365L5.66743 3.0511C5.6951 3.06494 5.71838 3.08622 5.73465 3.11255C5.75092 3.13887 5.75954 3.1692 5.75954 3.20015C5.75954 3.2311 5.75092 3.26143 5.73465 3.28776C5.71838 3.31408 5.6951 3.33536 5.66743 3.3492L4.42619 3.86686L3.9087 5.108C3.89488 5.13573 3.8736 5.15906 3.84725 5.17537C3.8209 5.19168 3.79053 5.20031 3.75954 5.20031C3.72855 5.20031 3.69817 5.19168 3.67183 5.17537C3.64548 5.15906 3.6242 5.13573 3.61037 5.108L3.09288 3.86686Z"
        fillOpacity="0.4"
      >
        {animate && (
          <animate
            attributeType="XML"
            attributeName="fill-opacity"
            values="0.4;0;0.4"
            keyTimes="0;0.5;1"
            dur=".8s"
            repeatCount="indefinite"
          ></animate>
        )}
      </path>
      <path
        d="M2.79996 13.2001L0.938168 12.4236C0.89665 12.4028 0.861734 12.3709 0.837331 12.3314C0.812928 12.292 0.800003 12.2465 0.800003 12.2C0.800003 12.1536 0.812928 12.1081 0.837331 12.0686C0.861734 12.0291 0.89665 11.9972 0.938168 11.9765L2.79996 11.2003L3.57617 9.33866C3.59691 9.29707 3.62883 9.26207 3.66835 9.23761C3.70787 9.21315 3.75343 9.2002 3.79991 9.2002C3.84639 9.2002 3.89195 9.21315 3.93148 9.23761C3.971 9.26207 4.00292 9.29707 4.02365 9.33866L4.79987 11.2003L6.66166 11.9765C6.70318 11.9972 6.73809 12.0291 6.7625 12.0686C6.7869 12.1081 6.79982 12.1536 6.79982 12.2C6.79982 12.2465 6.7869 12.292 6.7625 12.3314C6.73809 12.3709 6.70318 12.4028 6.66166 12.4236L4.79987 13.2001L4.02365 15.0617C4.00292 15.1033 3.971 15.1383 3.93148 15.1628C3.89195 15.1872 3.84639 15.2002 3.79991 15.2002C3.75343 15.2002 3.70787 15.1872 3.66835 15.1628C3.62883 15.1383 3.59691 15.1033 3.57617 15.0617L2.79996 13.2001Z"
        fillOpacity="0.6"
      >
        {animate && (
          <animate
            attributeType="XML"
            attributeName="fill-opacity"
            values="0.6;0;0.6"
            keyTimes="0;0.5;1"
            dur="1.2s"
            repeatCount="indefinite"
          ></animate>
        )}
      </path>
    </svg>
  );
};

export const SnipIcon2: React.FC<Props> = ({ size, className }: Props) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.6073 11.7694C10.9008 11.636 11.136 11.4008 11.2694 11.1073L12 9.5L12.7306 11.1073C12.864 11.4008 13.0992 11.636 13.3927 11.7694L15 12.5L13.3927 13.2306C13.0992 13.364 12.864 13.5992 12.7306 13.8927L12 15.5L11.2694 13.8927C11.136 13.5992 10.9008 13.364 10.6073 13.2306L9 12.5L10.6073 11.7694Z"
        fill="currentColor"
      />
      <path
        d="M22 13C22 10.3478 20.9464 7.8043 19.0711 5.92893C17.1957 4.05357 14.6522 3 12 3C11.241 3 10.491 3.08628 9.76292 3.2534M2 13C2 11.2859 2.44009 9.61714 3.2577 8.14483M6 13C6 11.4087 6.63214 9.88258 7.75736 8.75736C8.88258 7.63214 10.4087 7 12 7C13.5913 7 15.1174 7.63214 16.2426 8.75736C17.3679 9.88258 18 11.4087 18 13"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2 16C1.44772 16 1 16.4477 1 17C1 17.5523 1.44772 18 2 18H9C9.50415 18 9.83049 18.1595 10.07 18.3724C10.3302 18.6037 10.5358 18.9435 10.6887 19.3511C10.84 19.7546 10.92 20.1724 10.9608 20.499C10.9809 20.6598 10.9908 20.7922 10.9956 20.8816C10.998 20.9262 10.9991 20.9596 10.9996 20.9801L11 21.0006C11.0003 21.5526 11.4479 22 12 22C12.5521 22 12.9996 21.532 13 20.9801M13 20.9801C13.0005 20.9596 13.002 20.9262 13.0044 20.8816C13.0092 20.7922 13.0191 20.6598 13.0392 20.499C13.08 20.1724 13.16 19.7546 13.3113 19.3511C13.4642 18.9435 13.6698 18.6037 13.93 18.3724C14.1695 18.1595 14.4959 18 15 18H22C22.5522 18 23 17.5523 23 17C23 16.4477 22.5523 16 22 16H15C14.0041 16 13.2055 16.3405 12.6013 16.8776C12.366 17.0867 12.1676 17.3188 12 17.5602C11.8324 17.3188 11.634 17.0867 11.3987 16.8776C10.7945 16.3405 9.99585 16 9 16H2"
        fill="currentColor"
      />
      <path
        d="M7.94 5.44C7.94 6.26843 7.26843 6.94 6.44 6.94C5.61158 6.94 4.94 6.26843 4.94 5.44C4.94 4.61158 5.61158 3.94 6.44 3.94C7.26843 3.94 7.94 4.61158 7.94 5.44Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const PreviewIcon: React.FC<Props> = ({ size, className }: Props) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 16 16"
      fill="none"
      stroke="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.3335 2.00033V4.66699M14.6668 3.33366H12.0002M2.66683 11.3337V12.667M3.3335 12.0003H2.00016M6.62479 10.3337C6.56527 10.1029 6.44502 9.8924 6.27653 9.72392C6.10805 9.55544 5.8975 9.43518 5.66679 9.37566L1.57679 8.321C1.50701 8.30119 1.4456 8.25916 1.40186 8.20129C1.35813 8.14342 1.33447 8.07286 1.33447 8.00033C1.33447 7.92779 1.35813 7.85724 1.40186 7.79937C1.4456 7.74149 1.50701 7.69947 1.57679 7.67966L5.66679 6.62433C5.89742 6.56487 6.10792 6.44471 6.27639 6.27635C6.44486 6.108 6.56517 5.89759 6.62479 5.667L7.67946 1.577C7.69906 1.50694 7.74105 1.44522 7.799 1.40126C7.85696 1.35729 7.92771 1.3335 8.00046 1.3335C8.0732 1.3335 8.14395 1.35729 8.20191 1.40126C8.25987 1.44522 8.30185 1.50694 8.32146 1.577L9.37546 5.667C9.43497 5.89771 9.55523 6.10826 9.72371 6.27674C9.89219 6.44522 10.1027 6.56548 10.3335 6.625L14.4235 7.679C14.4938 7.6984 14.5558 7.74034 14.6 7.79838C14.6442 7.85643 14.6682 7.92737 14.6682 8.00033C14.6682 8.07329 14.6442 8.14423 14.6 8.20228C14.5558 8.26032 14.4938 8.30226 14.4235 8.32166L10.3335 9.37566C10.1027 9.43518 9.89219 9.55544 9.72371 9.72392C9.55523 9.8924 9.43497 10.1029 9.37546 10.3337L8.32079 14.4237C8.30118 14.4937 8.2592 14.5554 8.20124 14.5994C8.14328 14.6434 8.07254 14.6672 7.99979 14.6672C7.92704 14.6672 7.85629 14.6434 7.79834 14.5994C7.74038 14.5554 7.69839 14.4937 7.67879 14.4237L6.62479 10.3337Z"
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
