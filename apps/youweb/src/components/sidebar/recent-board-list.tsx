'use client';

import { BoardStatusEnum } from '@repo/common/types/board/types';
import { AnimatePresence, motion } from 'framer-motion';
import { useAtom, useAtomValue } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { useMemo } from 'react';
import { boardsAtom } from '@/hooks/useBoards';
import { cn } from '@/utils/utils';

import { BoardItem } from './board-list/board-item';

const MAX_RECENT_BOARD_COUNT = 6;

interface RecentBoardListProps {
  className?: string;
}

const collapseRecentBoardListAtom = atomWithStorage('collapseRecentBoardList', false);

export function RecentBoardList({ className }: RecentBoardListProps) {
  const boards = useAtomValue(boardsAtom);
  const [collapseRecentBoardList, setCollapseRecentBoardList] = useAtom(
    collapseRecentBoardListAtom,
  );

  // Sort boards by updated_at and get the most recent 6
  const recentBoards = useMemo(() => {
    return boards
      .filter((item) => item.status !== BoardStatusEnum.OTHER)
      .sort((a, b) => {
        const dateA = new Date(a.updated_at).getTime();
        const dateB = new Date(b.updated_at).getTime();
        return dateB - dateA; // Sort in descending order (most recent first)
      })
      .slice(0, MAX_RECENT_BOARD_COUNT);
  }, [boards]);

  if (recentBoards.length === 0) {
    return null;
  }

  return (
    <ul className={cn('flex flex-col items-start text-foreground', className)}>
      <li
        className="flex flex-row items-center justify-between w-full px-2 text-xs rounded-sm cursor-pointer group h-7 text-caption-fg hover:bg-snip-card"
        onClick={() => setCollapseRecentBoardList(!collapseRecentBoardList)}
      >
        <span>Recents</span>
        <span className="hidden group-hover:inline">
          {collapseRecentBoardList ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
        </span>
      </li>
      <AnimatePresence initial={false}>
        {!collapseRecentBoardList && (
          <motion.div
            initial={{ opacity: 0, height: 0, overflow: 'hidden' }}
            animate={{ opacity: 1, height: 'auto', overflow: 'visible' }}
            exit={{ opacity: 0, height: 0, overflow: 'hidden' }}
            transition={{ duration: 0.2, ease: 'easeInOut' }}
            className="w-full"
          >
            {recentBoards.map((item, _index) => {
              return <BoardItem key={item.id} board={item} />;
            })}
          </motion.div>
        )}
      </AnimatePresence>
    </ul>
  );
}
