import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { Link } from 'lucide-react';
import { DraggableFavoriteList } from '@/components/favorite/draggable-favorite-list';
import { NewTabTip } from '@/components/new-tab-tip';
import { cn } from '@/utils/utils';
import { AddContentButton } from './add-content-button';
import { BoardsListButton } from './boards-list-button';
import { HelpCenter } from './help-center';
import { InstallExtensionTip } from './install-extension-tip';
import { Logo } from './logo-button';
import { RecentBoardList } from './recent-board-list';
import { SearchButton } from './search-button';
import { UserProfile } from './user-profile';

interface SidebarPanelProps {
  userName: string;
  avatar: string;
  embedMode: boolean;
}

export function SidebarPanel({ userName, avatar, embedMode }: SidebarPanelProps) {
  const { trackButtonClick } = useTrackActions();

  return (
    <div className={cn('flex h-full w-full flex-col overflow-hidden')}>
      <div className="flex items-center justify-between">
        {embedMode && <Logo className="px-1" />}
        <div className="flex items-center gap-x-3 ">
          <SearchButton />
          <AddContentButton />
        </div>
      </div>

      <BoardsListButton className="mt-5" />

      <div className="flex-1 mb-2 overflow-y-auto">
        <DraggableFavoriteList className="mt-5" />
        <RecentBoardList className="mt-5" />
      </div>

      <div>
        <InstallExtensionTip />
        <a
          href="/overview"
          target="_blank"
          onClick={() => {
            trackButtonClick('sidebar_bottom_homepage_click');
          }}
          rel="noopener"
        >
          <div className="px-2 py-2 mb-0 text-sm rounded-md cursor-pointer hover:bg-accent">
            <div className="flex items-center w-full h-full">
              <Link size={16} className="mr-[10px]" />
              {'Homepage'}
              <div className="h-full ml-1 text-secondary-fg">
                <NewTabTip />
              </div>
            </div>
          </div>
        </a>
        <HelpCenter />
        <UserProfile userName={userName} avatar={avatar} />
      </div>
    </div>
  );
}
