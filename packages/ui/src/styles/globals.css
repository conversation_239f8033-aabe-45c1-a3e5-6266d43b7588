:root {
  --background: #f7f7f7;
  --foreground: #000000e0;

  --card: #ffffff;
  --card-foreground: #000000e0;

  --popover: #ffffff;
  --popover-foreground: #000000e0;

  --primary: #1f1f1f;
  --primary-foreground: #f9fafb;
  --primary-fg: #ffffff;

  --secondary: #a6a6a6;
  --secondary-foreground: #00000099;
  --secondary-fg: #00000099;

  /* muted 被改名为 select */
  /* 但是 shadcn 体系依然会生成 muted 的内容，需要保留这个 token，只是业务代码中不用 */
  --select: #02041a12;
  --muted: var(--select);
  --muted-foreground: #00000099;

  --active: var(--muted);
  --active-foreground: #02041829;

  --card-muted: #ffffff1f;

  --accent: #0204180a;
  --accent-foreground: #000000e0;

  --destructive: #ff382e;
  --destructive-foreground: #ffffff;

  --inactive: #ffffffa3;

  --border: #02041a29;
  --input: #02041829;
  --ring: #09090b;
  --divider: #02041a1f;

  --radius: 0.5rem;

  /** YouMind Custom CSS Variables **/
  --snip-card: #02041a0a;
  --caption: #00000066;
  --caption-fg: #00000066;

  --disabled: #0000003d;
  --disabled-fg: #0000003d;

  --primary-outline: #54d460;
  --primary-outline-foreground: #54d460;

  --block-background: #ffffff;

  --gray1: #666666;
  --gray2: #999999;
  --gray3: #c2c2c2;
  --ghost: #ffffff3d;

  --error: #f46273;

  --dark-blue-color: #3070f0;
  --blue-color: #61c5ff;
  --purple-color: #c39efd;
  --green-color: #9deb63;
  --red-color: #ff999c;
  --yellow-color: #ffdc52;
  --orange-color: #ff9500;

  --action-primary: var(--muted-foreground);
  --action-secondary: var(--caption);
  --action-primary-active: #ffffffe0;
  --action-secondary-active: #ffffff99;
  --action-primary-active-note: #242424e0;
  --action-secondary-active-note: #24242499;
  --link: #0063db;

  /* Board 的 Icon 颜色 */
  --function-gray: #8e8e93;
  --function-link: #007aff;
  --function-link-hover: #bdd2ea;
  --function-link-click: #1055a4;
  --function-text-link: #0f60bd;
  --function-mint: #5dd6cc;
  --function-green: #28cd41;
  --function-indigo: #5859d1;
  --function-purple: #a25ad9;
  --function-pink: #e8319e;
  --function-red: #ea4e43;
  --function-orange: #ff9500;
  --function-yellow: #ffcc00;
  --function-brown: #7e4f14;

  /* Assistant 的头像 */
  --assistant-avatar-icon-red: #ea4e43;
  --assistant-avatar-icon-brown: #c18b1f;
  --assistant-avatar-icon-yellow: #fab700;
  --assistant-avatar-icon-green: #298543;
  --assistant-avatar-icon-blue: #3884ff;
  --assistant-avatar-icon-purple: #a259d9;
  --assistant-avatar-icon-pink: #ff9edd;
  --assistant-avatar-icon-gray: #726e65;

  --assistant-avatar-emoji-red: #fed7d7;
  --assistant-avatar-emoji-brown: #ffe7c2;
  --assistant-avatar-emoji-yellow: #fff5ad;
  --assistant-avatar-emoji-green: #e7f2e3;
  --assistant-avatar-emoji-blue: #ccdaff;
  --assistant-avatar-emoji-purple: #ecdbff;
  --assistant-avatar-emoji-pink: #fde2f5;
  --assistant-avatar-emoji-gray: #f3f0e7;

  /* 黑白版本的 Assistant 的头像 */
  --assistant-icon: #3a3d40;

  --tweet-container-margin: 1.5rem 0;
  --tweet-header-font-size: 0.9375rem;
  --tweet-header-line-height: 1.25rem;
  --tweet-body-font-size: 1.25rem;
  --tweet-body-font-weight: 400;
  --tweet-body-line-height: 1.5rem;
  --tweet-body-margin: 0;
  --tweet-quoted-container-margin: 0.75rem 0;
  --tweet-quoted-body-font-size: 0.938rem;
  --tweet-quoted-body-font-weight: 400;
  --tweet-quoted-body-line-height: 1.25rem;
  --tweet-quoted-body-margin: 0.25rem 0 0.75rem 0;
  --tweet-info-font-size: 0.9375rem;
  --tweet-info-line-height: 1.25rem;
  --tweet-actions-font-size: 0.875rem;
  --tweet-actions-line-height: 1rem;
  --tweet-actions-font-weight: 500;
  --tweet-actions-icon-size: 1.25em;
  --tweet-actions-icon-wrapper-size: calc(var(--tweet-actions-icon-size) + 0.75em);
  --tweet-replies-font-size: 0.875rem;
  --tweet-replies-line-height: 1rem;
  --tweet-replies-font-weight: 500;
  --tweet-skeleton-gradient: linear-gradient(270deg, #fafafa, #eaeaea, #eaeaea, #fafafa);
  --tweet-border: 1px solid #cfd9de;
  --tweet-font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --tweet-font-color: #0f1419;
  --tweet-font-color-secondary: #536471;
  --tweet-bg-color: #fff;
  --tweet-bg-color-hover: #f7f9f9;
  --tweet-quoted-bg-color-hover: #00000008;
  --tweet-color-blue-primary: #1d9bf0;
  --tweet-color-blue-primary-hover: #1a8cd8;
  --tweet-color-blue-secondary: #006fd6;
  --tweet-color-blue-secondary-hover: #006fd61a;
  --tweet-color-red-primary: #f91880;
  --tweet-color-red-primary-hover: #f918801a;
  --tweet-color-green-primary: #00ba7c;
  --tweet-color-green-primary-hover: #00ba7c1a;
  --tweet-twitter-icon-color: var(--tweet-font-color);
  --tweet-verified-old-color: #829aab;
  --tweet-verified-blue-color: var(--tweet-color-blue-primary);
  --color-1: #ff4242;
  --color-2: #a142ff;
  --color-3: #42a1ff;
  --color-4: #42d0ff;
  --color-5: #a1ff42;

  --rich-panel-background: #ffffff;
}

@media (prefers-color-scheme: dark) {
  :root[data-env="mobile-embedded"] {
    --background: #202122;
    --foreground: #ffffffe0;

    --global-background: 236 12% 12% 1;

    --card: #1a1a1a;
    --card-foreground: #ffffffe0;

    --popover: #2e2e2e;
    --popover-foreground: #ffffffe0;

    --primary: #ffffff;
    --primary-foreground: #000000e0;
    --primary-fg: #1a1a1a;

    --secondary: #27272a;
    --secondary-fg: #ffffff99;
    --secondary-foreground: var(--secondary-fg);

    --select: #ffffff1a;

    --muted: var(--select);
    --muted-foreground: var(--secondary-fg);

    --accent: #ffffff0f;
    --accent-foreground: #ffffffe0;

    --destructive: #ff4238;
    --destructive-foreground: #ffffff;

    --inactive: #1a1a1aa3;

    --border: #ffffff33;
    --input: #ffffff33;
    --ring: #d4d4d8;
    --divider: #ffffff29;

    /** YouMind Custom CSS Variables **/
    --snip-card: #ffffff0f;
    --caption: #ffffff66;
    --caption-fg: #ffffff66;
    --disabled: #ffffff4d;
    --disabled-fg: #ffffff4d;

    --active: var(--muted);
    --active-foreground: #ffffffe0;

    --primary-outline: #63ca71;
    --primary-outline-foreground: #63ca71;

    --block-background: #242424;

    --gray1: #e5e5e5;
    --gray2: #ffffff;
    --gray3: #626464;
    --ghost: #0f0f0f3d;

    --error: #f46273;

    --action-primary: var(--foreground);
    --action-secondary: var(--caption);

    /* Board 的 Icon 颜色 */
    --function-gray: #98989d;
    --function-link: #0a84ff;
    --function-link-hover: #33a0ff;
    --function-link-click: #0062d9;
    --function-blue: var(--function-link);
    --function-mint: #71ede3;
    --function-green: #6dd459;
    --function-indigo: #5e5fe0;
    --function-purple: #b262ed;
    --function-pink: #e94862;
    --function-red: #ea5243;
    --function-orange: #ff9f0a;
    --function-yellow: #ffd60a;
    --function-brown: #b59469;
    --function-text-link: #0a84ff;

    /* 黑白版本的 Assistant 的头像 */
    --assistant-icon: #ffffff;

    --tweet-skeleton-gradient: linear-gradient(270deg, #15202b, #1e2732, #1e2732, #15202b);
    --tweet-border: 1px solid #425364;
    --tweet-font-family:
      -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    --tweet-font-color: #f7f9f9;
    --tweet-font-color-secondary: #8b98a5;
    --tweet-bg-color: #15202b;
    --tweet-bg-color-hover: #1e2732;
    --tweet-quoted-bg-color-hover: #ffffff08;
    --tweet-color-blue-primary: #1d9bf0;
    --tweet-color-blue-primary-hover: #1a8cd8;
    --tweet-color-blue-secondary: #6bc9fb;
    --tweet-color-blue-secondary-hover: #6bc9fb1a;
    --tweet-color-red-primary: #f91880;
    --tweet-color-red-primary-hover: #f918801a;
    --tweet-color-green-primary: #00ba7c;
    --tweet-color-green-primary-hover: #00ba7c1a;
    --tweet-twitter-icon-color: var(--tweet-font-color);
    --tweet-verified-old-color: #829aab;
    --tweet-verified-blue-color: #fff;
    --color-1: #ff4242;
    --color-2: #a142ff;
    --color-3: #42a1ff;
    --color-4: #42d0ff;
    --color-5: #a1ff42;

    --rich-panel-background: #000000e0;
  }
}

/* 使用 CSS 变量 */
:root {
  --scrollbar-thumb: #0000001a;
  --scrollbar-thumb-hover: #00000033;
  --color-1: #ff4242;
  --color-2: #a142ff;
  --color-3: #42a1ff;
  --color-4: #42d0ff;
  --color-5: #a1ff42;
}

::-webkit-scrollbar {
  width: 4px; /* 修改滚动条宽度,默认是16px */
  height: 4px; /* 设置横向滚动条的高度 */
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover);
}
/* Firefox */
* {
  scrollbar-color: var(--scrollbar-thumb) transparent;
}

* {
  @apply border-border;
}
body {
  @apply bg-background text-foreground;
}

/*---break---*/

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}
@keyframes marquee-vertical {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(calc(-100% - var(--gap)));
  }
}

/*---break---*/

.dark {
  --color-1: #ff4242;
  --color-2: #a142ff;
  --color-3: #42a1ff;
  --color-4: #42d0ff;
  --color-5: #a1ff42;
}

/*---break---*/

* {
  /* TODO: 需要问问这个啥情况 */
  /* @apply border-border outline-ring/50; */
}
body {
  @apply bg-background text-foreground;
}

/* 组件工具类 */

.footnote {
  @apply text-xs;
  line-height: 18px;
  font-weight: 400;
}

.caption {
  @apply text-xs;
  line-height: 18px;
  font-weight: 400;
}

.body {
  @apply text-sm;
  line-height: 20px;
  font-weight: 400;
}

.body-bold {
  @apply text-sm;
  line-height: 20px;
  font-weight: 500;
}

.body-strong {
  @apply text-sm;
  line-height: 20px;
  font-weight: 500;
}

.paragraph {
  @apply text-base;
  line-height: 26px;
  font-weight: 400;
}

.title {
  @apply text-base;
  line-height: 24px;
  font-weight: 500;
}

.title-large {
  @apply text-lg;
  font-size: 20px;
  line-height: 28px;
  font-weight: 590;
}

.headline3 {
  @apply text-2xl;
  line-height: 32px;
  font-weight: 500;
}

.headline2 {
  @apply text-2xl;
  line-height: 36px;
  font-weight: 500;
}

.headline1 {
  @apply text-3xl;
  line-height: 40px;
  font-weight: 500;
}

.display {
  @apply text-4xl;
  line-height: 48px;
  font-weight: 500;
}

.display-medium {
  font-size: 44px;
  font-style: normal;
  font-weight: 590;
  line-height: 52px;
}

.display-large {
  @apply text-5xl;
  line-height: 80px;
  font-weight: 500;
}

.text-ellipsis-line-1 {
  @apply overflow-hidden text-ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.text-ellipsis-line-2 {
  @apply overflow-hidden text-ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.text-ellipsis-line-3 {
  @apply overflow-hidden text-ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.text-ellipsis-line-4 {
  @apply overflow-hidden text-ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.block-media-grid-sizer,
.block-media-grid-item {
  width: 33.3333%;
}

/* Hide scrollbar for Chrome, Safari, and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge, and Firefox */
.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.smaller-h {
  h1 {
    font-size: 1.6em;
    line-height: 1.25em;
  }

  h2 {
    font-size: 1.2em;
    line-height: 1.51em;
  }

  h3 {
    font-size: 1em;
    line-height: 1.66em;
  }
}
