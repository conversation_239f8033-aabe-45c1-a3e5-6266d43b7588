'use client';

import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { Link } from 'react-router-dom';
import { cn } from '@/utils/utils';
import { BoardsIcon } from './boards-icon';

export function BoardsListButton(props: { className?: string }) {
  const { trackButtonClick } = useTrackActions();

  return (
    <Link
      to="/boards"
      onClick={() => {
        trackButtonClick('sidebar_boards_click');
      }}
    >
      <div
        className={cn(
          'flex items-center py-2 pl-1 rounded-md cursor-pointer gap-x-2 hover:bg-snip-card',
          props.className,
        )}
      >
        <BoardsIcon className="text-foreground" />
        <div className="text-sm">Boards</div>
      </div>
    </Link>
  );
}
