import { Button } from '@repo/ui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { Plus } from 'lucide-react';
import { StarIcon } from '../icon/snip';

export function AddContentButton() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="bg-card hover:bg-card" variant="outline" size="sm" iconOnly>
          <Plus size={16} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[200px]" align="start" side="bottom" alignOffset={-20}>
        <DropdownMenuItem>
          <StarIcon size={16} />
          New Board
          <div className="rounded-sm px-[5px] text-card">AI</div>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Plus size={16} />
          New from blank
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
