interface Props extends React.SVGAttributes<SVGElement> {
  className?: string;
  size?: number;
}

export function BoardsIcon({ size = 18, className, ...props }: Props) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M5.25 1.5H12.75M3.75 4.125H14.25M3.75 6.75H14.25C15.0784 6.75 15.75 7.42157 15.75 8.25V15C15.75 15.8284 15.0784 16.5 14.25 16.5H3.75C2.92157 16.5 2.25 15.8284 2.25 15V8.25C2.25 7.42157 2.92157 6.75 3.75 6.75Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.55042 10.9203C7.88058 10.7702 8.14519 10.5056 8.29526 10.1754L9 8.625L9.70474 10.1754C9.85481 10.5056 10.1194 10.7702 10.4496 10.9203L12 11.625L10.4496 12.3297C10.1194 12.4798 9.85481 12.7444 9.70474 13.0746L9 14.625L8.29526 13.0746C8.14519 12.7444 7.88058 12.4798 7.55042 12.3297L6 11.625L7.55042 10.9203Z"
        fill="currentColor"
      />
    </svg>
  );
}
