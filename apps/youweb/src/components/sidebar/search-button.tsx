'use client';

import { But<PERSON> } from '@repo/ui/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { Search } from 'lucide-react';
import { useState } from 'react';
import { SearchDialog } from '@/components/search';
import { useTranslation } from '@/hooks/useTranslation';

export function SearchButton() {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation('Sidebar');

  const { trackButtonClick } = useTrackActions();

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              iconOnly
              onClick={() => {
                trackButtonClick('search_button_click');
                setOpen(true);
              }}
            >
              <Search size={16} />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right" sideOffset={12} className="text-xs">
            {
              <span className="flex items-center gap-1">
                {t('searchTooltip')}
                <span className="ml-2 h-[14px] w-[14px] rounded-sm bg-[rgba(255,255,255,0.1)] px-1 text-[9px] text-background">
                  {'⌘'}
                </span>
                <span className="h-[14px] w-[14px] rounded-sm bg-[rgba(255,255,255,0.1)] px-1 text-[9px] text-background">
                  {'K'}
                </span>
              </span>
            }
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <SearchDialog active={open} onOpenChange={setOpen} />
    </>
  );
}
